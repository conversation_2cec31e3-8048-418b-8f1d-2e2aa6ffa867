{"zencoder.mcpServers": {"sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest", "--isolated", "--headless", "--browser", "firefox", "--user-agent", "\"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/125.0.6422.112 Safari/537.3\""]}}, "zencoder.enableRepoIndexing": true}